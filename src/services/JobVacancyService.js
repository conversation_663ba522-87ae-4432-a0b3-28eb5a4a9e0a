const { QdrantClient } = require('@qdrant/js-client-rest');
const AppService = require('./AppService');
const OnetService = require('./OnetService');
const {
  JobVacancy,
  JobTitle,
  User,
  VacancyGroupVariable,
  LlmMetadata,
  Bone,
  sequelize,
} = require('../models');
const JobVacanciesRepository = require('../repositories/JobVacanciesRepository');
const GenerateJobDescService = require('./job_vacancy/GenerateJobDescService');
const GoogleAiService = require('./external/GoogleAiService');
const JobTitleService = require('./JobTitleService');

class JobVacancyService extends AppService {
  constructor() {
    super();
    this.repository = new JobVacanciesRepository();
    this.onetService = new OnetService();
    this.googleAiService = new GoogleAiService();
    this.jobTitleService = new JobTitleService();
    this.qdrantClient = new QdrantClient({
      url: this.config.qdrantUrl,
      apiKey: this.config.qdrantApiKey,
    });
    this.generateJobDescService = new GenerateJobDescService({
      onetService: this.onetService,
      googleAiService: this.googleAiService,
      qdrantClient: this.qdrantClient,
    });
  }

  /**
   * Get all job vacancies
   * @param {Object} params - Query params (page, limit, filters, etc.)
   * @returns {Object} Job vacancies array and pagination info
   */
  async findAll(params = {}) {
    params.includes = ['jobLevel', 'bone', 'workArea', 'jobTitle'];
    const { rows, pagination } = await this.repository.findAll(params);

    const jobVacancyIds = rows.map(row => row.id);
    const countsById = await this.getUjvCounts(jobVacancyIds);

    return { job_vacancies: rows, pagination, countsById };
  }

  /**
   * Find a job vacancy by ID
   * @param {number} id - Job vacancy ID
   * @returns {Object}
   * @throws {NotFoundError} If job vacancy is not found
   */
  async findById(id) {
    const repoParams = { id, includes: ['jobLevel', 'bone', 'workArea', 'jobTitle'] };
    const vacancy = await this.repository.findOne(repoParams);
    this.exists(vacancy, 'Job vacancy not found');

    let referenceUsers = [];
    if (vacancy.related_user_ids) {
      referenceUsers = await User.findAll({
        where: { id: vacancy.related_user_ids },
        attributes: ['id', 'name'],
      });
    }

    return { vacancy, referenceUsers };
  }

  /**
   * Create a new job vacancy
   * @param {Object} data - Job vacancy data
   * @returns {Object} Created job vacancy
   * @throws {NotFoundError} If job title is not found
   */
  async create(data) {
    if (!data.name && !data.job_title_id) {
      this.assert(false, 'Name or job title is required');
    }

    // Fetch job title name if job_title_id is provided but name is not
    let jobTitleName = data.name;
    if (!jobTitleName && data.job_title_id) {
      const jobTitle = await JobTitle.findByPk(data.job_title_id);
      this.exists(jobTitle, 'Job title not found');
      jobTitleName = jobTitle.name;
    }

    const vacancyData = {
      ...data,
      name: jobTitleName,
      status: 'generating_jobdesc',
    };

    let vacancy;
    await this.transaction(async transaction => {
      const jobTitle = await this.jobTitleService.upsertPrefilledDetails(
        {
          id: data.job_title_id,
          name: vacancyData.name,
          prefilled_details: {
            job_level_id: vacancyData.job_level_id,
            role_summary: vacancyData.role_summary,
          },
        },
        transaction,
      );

      vacancy = await JobVacancy.create(
        {
          ...vacancyData,
          job_title_id: jobTitle.id,
        },
        { transaction },
      );
    });

    this.setJobDesc(vacancy);
    return vacancy;
  }

  /**
   * Update a job vacancy
   * @param {number} id - Job vacancy ID
   * @param {Object} data - Job vacancy data
   * @returns {Object} Updated job vacancy
   * @throws {NotFoundError} If job vacancy is not found
   */
  async update(id, data) {
    const vacancy = await this.repository.findOne({ id });
    this.exists(vacancy, 'Job vacancy not found');

    let jobTitleName = data.name;
    if (!jobTitleName && data.job_title_id) {
      const jobTitle = await JobTitle.findByPk(data.job_title_id);
      this.exists(jobTitle, 'Job title not found');
      jobTitleName = jobTitle.name;
    }

    const followupAction = data.followup_action;
    delete data.followup_action;

    if (followupAction) {
      const isDraft = vacancy.status === 'draft';
      const isActive = vacancy.status === 'active';
      const errorMessage = `Cannot update this vacancy, current status: ${vacancy.status}`;
      this.assert(isDraft || isActive, errorMessage);
    }

    const actionMapping = {
      generate_jobdesc: 'generating_jobdesc',
      generate_job_variables: 'generating_job_variables',
    };

    const additionalData = {};
    additionalData.status = actionMapping[followupAction];
    additionalData.name = jobTitleName || vacancy.name;

    const vacancyData = {
      ...data,
      ...additionalData,
    };

    await this.transaction(async transaction => {
      await vacancy.update(vacancyData, { transaction });
      await this.jobTitleService.upsertPrefilledDetails(
        {
          id: vacancy.job_title_id,
          name: vacancy.name,
          prefilled_details: {
            job_level_id: vacancy.job_level_id,
            role_summary: vacancy.role_summary,
          },
        },
        transaction,
      );
    });

    if (followupAction === 'generate_jobdesc') {
      this.setJobDesc(vacancy);
    } else if (followupAction === 'generate_job_variables') {
      this.setVacancyGroupVariables(vacancy);
    }

    return vacancy;
  }

  async delete(id) {
    const vacancy = await this.repository.findOne({ id });
    this.exists(vacancy, 'Job vacancy not found');

    await vacancy.destroy();
  }

  async setJobDesc(vacancy) {
    try {
      const jobTitleName = vacancy.name;
      const relatedUserIds = vacancy.related_user_ids;
      const jobLevelName = vacancy.jobLevel ? vacancy.jobLevel.name : '';
      const roleSummary = vacancy.role_summary;

      const generatedJobDesc = await this.generateJobDescService.generateJobDesc(
        jobTitleName,
        relatedUserIds,
        jobLevelName,
        roleSummary,
      );

      await vacancy.update({
        job_desc: generatedJobDesc.jobDescription.key_responsibilities,
        related_onetsoc_codes: generatedJobDesc.onetsocCodes,
        status: 'draft',
        detailed_descriptions: generatedJobDesc.jobDescription,
      });
    } catch (error) {
      console.error('Error in setJobDesc:', error);
      await vacancy.update({ status: 'draft' });
      // If we has error alerting, we should add them here
    }
  }

  async setVacancyGroupVariables(vacancy) {
    try {
      const ksao = await this.generateKsao(vacancy);

      const vgvRecords = await this.ksaoMatching({
        ksao,
        jobVacancyId: vacancy.id,
        boneId: vacancy.bone_id,
      });

      if (vgvRecords.length > 0) {
        await VacancyGroupVariable.bulkCreate(vgvRecords, {
          updateOnDuplicate: [
            'keyword_match_count',
            'keyword_total_count',
            'match_type',
            'weight',
            'bone_value',
          ],
        });
      }

      await vacancy.update({
        ksao,
        status: 'draft',
      });
    } catch (error) {
      console.error('Error setting vacancy group variables:', error);
      await vacancy.update({ status: 'draft' });
    }
  }

  async generateKsao(vacancy) {
    const vacancyName = vacancy.name;
    const jobDetails = vacancy.detailed_descriptions || {};
    const jobDesc = jobDetails.key_responsibilities || vacancy.job_desc;
    const onetsocCodes = vacancy.related_onetsoc_codes;
    const occupations = await this.getOccupationsData(onetsocCodes);

    const [systemPrompt, userPrompt] = await Promise.all([
      this.getSystemPrompt(),
      this.getUserPrompt(vacancyName, jobDesc, occupations, jobDetails),
    ]);

    const aiParams = {
      model: 'gemini-2.5-flash',
      contents: [{ role: 'user', parts: [{ text: userPrompt }] }],
      config: {
        temperature: 0.2,
        responseMimeType: 'application/json',
        thinkingConfig: { thinkingBudget: -1 },
        systemInstruction: [{ text: systemPrompt }],
      },
    };

    const response = await this.googleAiService.generateContent(aiParams);

    LlmMetadata.create({
      request: aiParams,
      responses: response,
      action_type: 'generate_ksao',
    });

    const ksao = JSON.parse(response.candidates[0].content.parts[0].text);
    return ksao;
  }

  async getOccupationsData(onetsocCodes) {
    const onetResults = await Promise.all([
      this.onetService.getOccupations(onetsocCodes),
      this.onetService.getKnowledges(onetsocCodes),
      this.onetService.getSkills(onetsocCodes),
      this.onetService.getAbilities(onetsocCodes),
      this.onetService.getInterests(onetsocCodes),
      this.onetService.getWorkValues(onetsocCodes),
      this.onetService.getWorkStyles(onetsocCodes),
    ]);

    const occupations = onetResults[0];
    const referenceData = {
      knowledges: onetResults[1],
      skills: onetResults[2],
      abilities: onetResults[3],
      interests: onetResults[4],
      work_values: onetResults[5],
      work_styles: onetResults[6],
    };

    await Promise.all(
      Object.keys(referenceData).map(async key => {
        await Promise.all(
          referenceData[key].map(async item => {
            const onetsocCode = item.onetsoc_code;
            delete item.onetsoc_code;

            occupations[onetsocCode][key] ||= [];
            occupations[onetsocCode][key].push(item);
          }),
        );
      }),
    );

    return occupations;
  }

  async getSystemPrompt() {
    return Promise.resolve(`
      ## Role and Context
      You are an expert HR professional specializing in job analysis and competency modeling. Your expertise includes translating job requirements into structured Knowledge, Skills, Abilities, and Other characteristics (KSAO) frameworks that align with industry standards and O*NET occupational data.

      ## Task Overview
      Create a comprehensive KSAO profile for a specific job vacancy by analyzing job descriptions and leveraging O*NET occupational data as supporting reference material.

      ## Input Structure
      You will receive:
      1. **Job Title**: The specific position title
      2. **Job Descriptions**: Detailed list of key job responsibilities, qualifications, skill and competencies, and success metrics
      3. **Related O*NET Data**: Occupational information including:
        - Knowledge areas
        - Skills requirements
        - Abilities needed
        - Work interests
        - Work values
        - Work styles

      ## Instructions

      ### Step 1: Analysis
      - Carefully analyze the provided job descriptions to identify core responsibilities and requirements
      - Cross-reference with O*NET data to identify relevant competencies
      - Prioritize job-specific requirements while using O*NET data to fill gaps and validate decisions

      ### Step 2: KSAO Development
      Create four distinct categories using these definitions and formatting guidelines:

      **Knowledge (K)**: Body of facts and information someone must know
      - Write as short, specific topic areas or subject matter domains
      - Use concise phrases (3-8 words typically)
      - Focus on what someone needs to know, not how they apply it
      - Examples: "Brand positioning and messaging", "Employment law and regulations", "Financial analysis principles"

      **Skills (S)**: Learned proficiencies that can be demonstrated
      - Write as actionable capabilities or techniques
      - Use verb phrases when appropriate
      - Focus on what someone can do or perform
      - Examples: "Develop brand strategy", "Conduct market research", "Data analysis and reporting"

      **Abilities (A)**: Enduring attributes that support performance
      - Write as inherent capacities or traits
      - Often start with "Ability to..." but keep concise
      - Focus on cognitive, physical, or interpersonal capabilities
      - Examples: "Strategic thinking", "Ability to influence stakeholders", "Analytical reasoning"

      **Other Characteristics (O)**: Traits, motivations, values, or work styles
      - Write as personal attributes or orientations
      - Focus on personality traits, work preferences, and motivational factors
      - Examples: "Creative mindset", "Detail orientation", "Growth mindset"

      ### Step 3: Quality Standards
      - Keep each item concise (typically 2-6 words, maximum 8 words)
      - Ensure items are specific and job-relevant
      - Avoid lengthy explanations or examples in the KSAO items themselves
      - Maintain clear distinction between categories
      - Aim for 6-10 items per category for comprehensive coverage
      - Prioritize the most critical competencies for job success

      ## Output Format
      Return your response as a valid JSON object with the following structure:
      {
        "knowledges": [
          "string"
        ],
        "skills": [
          "string"
        ],
        "abilities": [
          "string"
        ],
        "other_characteristics": [
          "string"
        ]
      }

      ## Example Output (Brand Manager)
      {
        "knowledges": [
          "Brand positioning and messaging",
          "Market research methodologies",
          "Competitor analysis",
          "Consumer behavior insights",
          "Brand guidelines development",
          "Marketing analytics tools",
          "Digital marketing channels",
          "Campaign planning processes"
        ],
        "skills": [
          "Develop brand strategy",
          "Conduct market research",
          "Create brand guidelines",
          "Monitor brand metrics",
          "Data analysis and reporting",
          "Communication and storytelling",
          "Cross-functional collaboration",
          "Stakeholder presentation"
        ],
        "abilities": [
          "Strategic thinking",
          "Analytical reasoning",
          "Creative problem-solving",
          "Ability to influence stakeholders",
          "Ability to balance creativity with data",
          "Cross-functional collaboration",
          "Adaptability to changing markets"
        ],
        "other_characteristics": [
          "Creative mindset",
          "Passion for brand building",
          "Growth mindset",
          "Consumer empathy",
          "Detail orientation",
          "Leadership presence",
          "Innovation-focused"
        ]
      }

      ## Important Notes
      - Base your KSAO primarily on the job descriptions provided
      - Use O*NET data as supporting reference to enhance and validate your analysis
      - Keep all items concise and specific - avoid lengthy explanations
      - Focus on the most essential competencies for job success
      - Ensure all items are directly relevant to the specific job vacancy
      - Maintain professional HR terminology and standards
      - Double-check that your output is valid JSON format
    `);
  }

  async getUserPrompt(vacancyName, jobDesc, occupations, jobDetails) {
    let userPrompt = `# Job Title\n${vacancyName}\n\n`;

    // higher function
    const mapJobDetails = array => {
      let result = '';
      array.forEach(item => {
        result += `- ${item}\n\n`;
      });
      return result;
    };

    if (jobDetails) {
      userPrompt += `# Job Details\n${jobDetails}\n\n`;
      userPrompt += '## Job Key Responsibilities\n';
      userPrompt += mapJobDetails(jobDetails.key_responsibilities);
      userPrompt += '## Job Qualifications\n';
      userPrompt += mapJobDetails(jobDetails.qualifications);
      userPrompt += '## Job Skill & Competencies\n';
      userPrompt += mapJobDetails(jobDetails.competencies);
      userPrompt += '## Job Success Metrics\n';
      userPrompt += mapJobDetails(jobDetails.success_metrics);
    } else {
      userPrompt += `# Job Descriptions\n`;
      userPrompt += mapJobDetails(jobDesc);
    }

    userPrompt += '\n# Related O*NET Data\n';

    await Promise.all(
      Object.values(occupations).map(async occupation => {
        userPrompt += `## Occupation: ${occupation.title}\n`;
        userPrompt += `### Description\n${occupation.description}\n\n`;

        await Promise.all(
          ['knowledges', 'skills', 'abilities', 'interests', 'work_values', 'work_styles'].map(
            async key => {
              if (occupation[key] && occupation[key].length > 0) {
                userPrompt += `### ${key.replace('_', ' ')}\n`;
                userPrompt += `\`\`\`json\n`;
                userPrompt += JSON.stringify(occupation[key], null, 2);
                userPrompt += `\n\`\`\`\n\n`;
              }
            },
          ),
        );
      }),
    );

    return Promise.resolve(userPrompt);
  }

  async ksaoMatching({ ksao, jobVacancyId, boneId }) {
    const vgvRecords = [];

    const [jgvs, flattenedKsao] = await Promise.all([
      this.getJobGroupVariables(),
      this.flattenKsao(ksao),
    ]);

    let boneName;
    if (boneId) {
      boneName = (await Bone.findByPk(boneId)).name;
    }

    // TODO: move to db later
    const boneMappings = {
      1: {
        Business: 7,
        Functional: 5,
        Professional: 6,
      },
      2: {
        Business: 6,
        Functional: 4,
        Professional: 3,
      },
      3: {
        Business: 5,
        Functional: 4,
        Professional: 7,
      },
      4: {
        Business: 6,
        Functional: 7,
        Professional: 5,
      },
      5: {
        Business: 7,
        Functional: 5,
        Professional: 5,
      },
      6: {
        Business: 7,
        Functional: 5,
        Professional: 6,
      },
      7: {
        Business: 7,
        Functional: 5,
        Professional: 4,
      },
      8: {
        Business: 6,
        Functional: 5,
        Professional: 5,
      },
    };

    await Promise.all(
      jgvs.map(async jgv => {
        const matchCount = jgv.keywords.reduce((acc, keyword) => {
          return acc + (flattenedKsao.includes(keyword) ? 1 : 0);
        }, 0);

        const boneMapping = boneMappings[jgv.id];
        const boneValue = boneMapping[boneName] || 0;

        vgvRecords.push({
          job_vacancy_id: jobVacancyId,
          job_group_variable_id: jgv.id,
          keyword_match_count: matchCount,
          keyword_total_count: jgv.keywords.length,
          match_type: 'weight',
          weight: 1 / jgvs.length,
          bone_value: boneValue,
        });
      }),
    );

    return vgvRecords;
  }

  async getJobGroupVariables() {
    return sequelize.query(
      'SELECT id, keywords FROM job_group_variables WHERE deleted_at IS NULL',
      {
        type: sequelize.QueryTypes.SELECT,
      },
    );
  }

  async flattenKsao(ksao) {
    return Object.values(ksao)
      .flat()
      .map(item => item.toLowerCase())
      .join(';');
  }

  async averagingVarGroup(jobVacancyId) {
    const vgvList = await VacancyGroupVariable.findAll({
      where: { job_vacancy_id: jobVacancyId },
    });

    await Promise.all(
      vgvList.map(async vgv => {
        const sql = `
          WITH average_group_scores AS (
            SELECT ujvs.user_id,
              AVG(ujvs.match_score) AS avg_score
            FROM user_vacancy_variable_scores ujvs
            WHERE ujvs.job_vacancy_id = :job_vacancy_id
              AND ujvs.job_variable_id IN (
                SELECT id
                FROM job_variables
                WHERE job_group_variable_id = :job_group_variable_id
                  AND deleted_at IS NULL
              )
              AND ujvs.match_score IS NOT NULL
              AND ujvs.deleted_at IS NULL
            GROUP BY ujvs.user_id
          )

          INSERT INTO user_vacancy_group_variables (
            user_id,
            vacancy_group_variable_id,
            average_match_score,
            created_at,
            updated_at
          )

          SELECT u.id AS user_id,
            :vacancy_group_variable_id AS vacancy_group_variable_id,
            COALESCE(ags.avg_score, 0) AS average_match_score,
            NOW() AS created_at,
            NOW() AS updated_at
          FROM users u
          LEFT JOIN average_group_scores ags ON u.id = ags.user_id
          WHERE u.deleted_at IS NULL

          ON CONFLICT (user_id, vacancy_group_variable_id)
          DO UPDATE SET average_match_score = EXCLUDED.average_match_score,
            updated_at = EXCLUDED.updated_at;
        `;

        await sequelize.query(sql, {
          replacements: {
            vacancy_group_variable_id: vgv.id,
            job_group_variable_id: vgv.job_group_variable_id,
            job_vacancy_id: jobVacancyId,
          },
          type: sequelize.QueryTypes.RAW,
        });
      }),
    );
  }

  async weightingMatchRate(jobVacancyId) {
    const sql = `
      WITH user_weighted_scores AS (
        SELECT uvgv.user_id,
          SUM(uvgv.average_match_score * vgv.weight)
            FILTER(WHERE vgv.match_type = 'weight')
            AS match_rate,
          COUNT(*) FILTER(
            WHERE vgv.match_type = 'filter'
            AND uvgv.average_match_score < 100
          ) AS below_threshold_count
        FROM vacancy_group_variables vgv
        JOIN user_vacancy_group_variables uvgv ON vgv.id = uvgv.vacancy_group_variable_id
        WHERE vgv.job_vacancy_id = :job_vacancy_id
          AND vgv.deleted_at IS NULL
          AND uvgv.deleted_at IS NULL
        GROUP BY uvgv.user_id
      )

      INSERT INTO user_job_vacancies (
        user_id,
        job_vacancy_id,
        match_rate,
        status,
        created_at,
        updated_at
      )

      SELECT
        uws.user_id,
        :job_vacancy_id AS job_vacancy_id,
        uws.match_rate,
        CASE
          WHEN uws.match_rate >= 85
            AND uws.below_threshold_count = 0
            THEN 'matched'
          ELSE 'not_matched'
        END AS status,
        NOW() AS created_at,
        NOW() AS updated_at
      FROM user_weighted_scores uws

      ON CONFLICT (user_id, job_vacancy_id)
      DO UPDATE SET match_rate = EXCLUDED.match_rate,
        status = EXCLUDED.status,
        updated_at = EXCLUDED.updated_at;
    `;

    await sequelize.query(sql, {
      replacements: { job_vacancy_id: jobVacancyId },
      type: sequelize.QueryTypes.RAW,
    });
  }

  async getUjvCounts(jobVacancyIds) {
    if (jobVacancyIds.length === 0) {
      return {};
    }

    const countSql = `
      SELECT job_vacancy_id,
        status,
        COUNT(*) AS ujv_count
      FROM user_job_vacancies
      WHERE job_vacancy_id IN (:job_vacancy_ids)
        AND deleted_at IS NULL
      GROUP BY job_vacancy_id, status;
    `;

    const countResult = await sequelize.query(countSql, {
      replacements: { job_vacancy_ids: jobVacancyIds },
      type: sequelize.QueryTypes.SELECT,
    });

    const countsById = jobVacancyIds.reduce((acc, jobVacancyId) => {
      acc[jobVacancyId] = {
        matched: 0,
        shortlisted: 0,
        approved: 0,
        appointed: 0,
        not_matched: 0,
      };
      return acc;
    }, {});

    countResult.forEach(count => {
      if (countsById[count.job_vacancy_id]) {
        countsById[count.job_vacancy_id][count.status] = count.ujv_count;
      }
    });

    return countsById;
  }
}

module.exports = JobVacancyService;
