const config = require('../config/config');
const InvalidError = require('../errors/InvalidError');
const NotFoundError = require('../errors/NotFoundError');
const { sequelize } = require('../models');

class AppService {
  constructor() {
    this.config = config;
  }

  assert(condition, message) {
    if (!condition) {
      throw new InvalidError(message);
    }
  }

  exists(object, message) {
    if (!object) {
      throw new NotFoundError(message);
    }
  }

  async transaction(callback, parentTransaction = null) {
    const options = {};
    parentTransaction ||= sequelize.constructor._cls?.get('transaction');
    if (parentTransaction) {
      console.log('Using parent transaction');
      options.transaction = parentTransaction;
    }

    return await sequelize.transaction(options, async transaction => {
      return await callback(transaction);
    });
  }
}

module.exports = AppService;
