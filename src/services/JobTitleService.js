const AppService = require('./AppService');
const JobTitlesRepository = require('../repositories/JobTitlesRepository');
const { JobTitle } = require('../models');

class JobTitleService extends AppService {
  constructor() {
    super();
    this.repository = new JobTitlesRepository();
  }

  /**
   * Get all job titles with pagination
   * @param {Object} params - Query params (page, limit, etc.)
   * @returns {Object} - Job titles array and pagination info
   */
  async findAll(params = {}) {
    const { rows, pagination } = await this.repository.findAll(params);
    return { job_titles: rows, pagination };
  }

  async upsertPrefilledDetails(data, transaction) {
    let jobTitle;

    if (data.id) {
      jobTitle = await JobTitle.findByPk(data.id, { paranoid: false, transaction });
    } else if (data.name) {
      jobTitle = await JobTitle.findOne({
        where: { name: data.name },
        paranoid: false,
        transaction,
      });
    } else {
      console.warn('No job title id or name provided, skipping upsert');
      return null;
    }

    if (!jobTitle) {
      jobTitle = await JobTitle.create(data, { transaction });
      return jobTitle;
    }

    const newPrefilledDetails = { ...(data.prefilled_details || {}) };
    const prefilledDetails = { ...(jobTitle.prefilled_details || {}) };

    Object.keys(newPrefilledDetails).forEach(key => {
      if (newPrefilledDetails[key]) {
        prefilledDetails[key] = newPrefilledDetails[key];
      }
    });

    await jobTitle.update(
      {
        prefilled_details: prefilledDetails,
        deleted_at: null,
      },
      { transaction },
    );

    return jobTitle;
  }
}

module.exports = JobTitleService;
